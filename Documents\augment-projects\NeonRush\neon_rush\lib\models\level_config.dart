import '../core/constants.dart';
import 'neon_theme.dart';

/// Configuration for a game level
class LevelConfig {
  final int levelNumber;
  final String title;
  final String description;
  final GameMode mode;
  final Duration duration;
  final int goal;
  final double speed;
  final NeonTheme theme;
  final DifficultyLevel difficulty;
  final int xpReward;
  final int tokenReward;
  final List<String> instructions;
  final Map<String, dynamic> modeSpecificConfig;

  const LevelConfig({
    required this.levelNumber,
    required this.title,
    required this.description,
    required this.mode,
    required this.duration,
    required this.goal,
    required this.speed,
    required this.theme,
    required this.difficulty,
    required this.xpReward,
    required this.tokenReward,
    required this.instructions,
    this.modeSpecificConfig = const {},
  });

  /// Creates a copy of this config with modified properties
  LevelConfig copyWith({
    int? levelNumber,
    String? title,
    String? description,
    GameMode? mode,
    Duration? duration,
    int? goal,
    double? speed,
    NeonTheme? theme,
    DifficultyLevel? difficulty,
    int? xpReward,
    int? tokenReward,
    List<String>? instructions,
    Map<String, dynamic>? modeSpecificConfig,
  }) {
    return LevelConfig(
      levelNumber: levelNumber ?? this.levelNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      mode: mode ?? this.mode,
      duration: duration ?? this.duration,
      goal: goal ?? this.goal,
      speed: speed ?? this.speed,
      theme: theme ?? this.theme,
      difficulty: difficulty ?? this.difficulty,
      xpReward: xpReward ?? this.xpReward,
      tokenReward: tokenReward ?? this.tokenReward,
      instructions: instructions ?? this.instructions,
      modeSpecificConfig: modeSpecificConfig ?? this.modeSpecificConfig,
    );
  }

  /// Converts config to JSON
  Map<String, dynamic> toJson() {
    return {
      'levelNumber': levelNumber,
      'title': title,
      'description': description,
      'mode': mode.name,
      'duration': duration.inMilliseconds,
      'goal': goal,
      'speed': speed,
      'theme': theme.toJson(),
      'difficulty': difficulty.name,
      'xpReward': xpReward,
      'tokenReward': tokenReward,
      'instructions': instructions,
      'modeSpecificConfig': modeSpecificConfig,
    };
  }

  /// Creates config from JSON
  factory LevelConfig.fromJson(Map<String, dynamic> json) {
    return LevelConfig(
      levelNumber: json['levelNumber'],
      title: json['title'],
      description: json['description'],
      mode: GameMode.values.firstWhere((m) => m.name == json['mode']),
      duration: Duration(milliseconds: json['duration']),
      goal: json['goal'],
      speed: json['speed'].toDouble(),
      theme: NeonTheme.fromJson(json['theme']),
      difficulty: DifficultyLevel.values.firstWhere((d) => d.name == json['difficulty']),
      xpReward: json['xpReward'],
      tokenReward: json['tokenReward'],
      instructions: List<String>.from(json['instructions']),
      modeSpecificConfig: Map<String, dynamic>.from(json['modeSpecificConfig'] ?? {}),
    );
  }
}

/// Predefined level configurations
class LevelConfigs {
  static final List<LevelConfig> allLevels = [
    // Level 1: Tap
    LevelConfig(
      levelNumber: 1,
      title: 'Neon Touch',
      description: 'Tap the glowing dots to score points',
      mode: GameMode.tap,
      duration: const Duration(seconds: 30),
      goal: 20,
      speed: 1.0,
      theme: NeonThemes.cyberBlue,
      difficulty: DifficultyLevel.easy,
      xpReward: 100,
      tokenReward: 25,
      instructions: [
        'Tap the glowing neon dots',
        'Score 20 points to complete',
        'Don\'t let them disappear!'
      ],
      modeSpecificConfig: {
        'dotSize': 60.0,
        'spawnRate': 1.5,
        'dotLifetime': 3.0,
      },
    ),

    // Level 2: Swipe
    LevelConfig(
      levelNumber: 2,
      title: 'Neon Swipe',
      description: 'Swipe objects away from the edges',
      mode: GameMode.swipe,
      duration: const Duration(seconds: 45),
      goal: 15,
      speed: 1.2,
      theme: NeonThemes.neonPink,
      difficulty: DifficultyLevel.easy,
      xpReward: 120,
      tokenReward: 30,
      instructions: [
        'Swipe objects away from edges',
        'Prevent 15 objects from hitting edges',
        'Quick reflexes needed!'
      ],
      modeSpecificConfig: {
        'objectSpeed': 100.0,
        'spawnRate': 2.0,
        'swipeThreshold': 50.0,
      },
    ),

    // Level 3: Hold
    LevelConfig(
      levelNumber: 3,
      title: 'Neon Hold',
      description: 'Hold the glow without releasing',
      mode: GameMode.hold,
      duration: const Duration(seconds: 60),
      goal: 45,
      speed: 1.0,
      theme: NeonThemes.electricGreen,
      difficulty: DifficultyLevel.medium,
      xpReward: 150,
      tokenReward: 35,
      instructions: [
        'Hold down on the glowing area',
        'Keep holding for 45 seconds total',
        'Don\'t release too early!'
      ],
      modeSpecificConfig: {
        'holdThreshold': 0.8,
        'glowPulseRate': 1.5,
        'penaltyTime': 2.0,
      },
    ),

    // Level 4: Avoid
    LevelConfig(
      levelNumber: 4,
      title: 'Neon Dodge',
      description: 'Dodge incoming neon enemies',
      mode: GameMode.avoid,
      duration: const Duration(seconds: 60),
      goal: 50,
      speed: 1.5,
      theme: NeonThemes.fireOrange,
      difficulty: DifficultyLevel.medium,
      xpReward: 180,
      tokenReward: 40,
      instructions: [
        'Drag your orb to avoid enemies',
        'Survive for 50 seconds',
        'Don\'t get hit!'
      ],
      modeSpecificConfig: {
        'enemySpeed': 150.0,
        'enemySpawnRate': 1.0,
        'playerSize': 40.0,
        'enemySize': 30.0,
      },
    ),

    // Level 5: Memory
    LevelConfig(
      levelNumber: 5,
      title: 'Neon Memory',
      description: 'Repeat the color sequence',
      mode: GameMode.memory,
      duration: const Duration(seconds: 90),
      goal: 8,
      speed: 1.0,
      theme: NeonThemes.purpleHaze,
      difficulty: DifficultyLevel.medium,
      xpReward: 200,
      tokenReward: 45,
      instructions: [
        'Watch the sequence of colors',
        'Repeat it back correctly',
        'Complete 8 sequences'
      ],
      modeSpecificConfig: {
        'sequenceLength': 3,
        'showTime': 1.0,
        'maxSequenceLength': 6,
      },
    ),

    // Level 6: Reaction
    LevelConfig(
      levelNumber: 6,
      title: 'Neon Reaction',
      description: 'Tap only when the signal is green',
      mode: GameMode.reaction,
      duration: const Duration(seconds: 60),
      goal: 25,
      speed: 1.3,
      theme: NeonThemes.electricGreen,
      difficulty: DifficultyLevel.medium,
      xpReward: 220,
      tokenReward: 50,
      instructions: [
        'Wait for the green signal',
        'Tap immediately when green',
        'Don\'t tap on red!'
      ],
      modeSpecificConfig: {
        'minWaitTime': 1.0,
        'maxWaitTime': 4.0,
        'reactionWindow': 0.5,
      },
    ),

    // Level 7: Drag
    LevelConfig(
      levelNumber: 7,
      title: 'Neon Drag',
      description: 'Drag orbs to their targets',
      mode: GameMode.drag,
      duration: const Duration(seconds: 75),
      goal: 20,
      speed: 1.4,
      theme: NeonThemes.neonPink,
      difficulty: DifficultyLevel.hard,
      xpReward: 250,
      tokenReward: 55,
      instructions: [
        'Drag orbs to matching targets',
        'Complete 20 successful drags',
        'Speed matters!'
      ],
      modeSpecificConfig: {
        'targetTolerance': 30.0,
        'orbCount': 3,
        'timeLimit': 5.0,
      },
    ),

    // Level 8: Spin
    LevelConfig(
      levelNumber: 8,
      title: 'Neon Spin',
      description: 'Rotate rings to match gaps',
      mode: GameMode.spin,
      duration: const Duration(seconds: 90),
      goal: 15,
      speed: 1.6,
      theme: NeonThemes.goldRush,
      difficulty: DifficultyLevel.hard,
      xpReward: 280,
      tokenReward: 60,
      instructions: [
        'Rotate the rings',
        'Align gaps to let orbs pass',
        'Complete 15 alignments'
      ],
      modeSpecificConfig: {
        'ringCount': 3,
        'rotationSpeed': 90.0,
        'gapSize': 60.0,
      },
    ),

    // Level 9: Deflect
    LevelConfig(
      levelNumber: 9,
      title: 'Neon Deflect',
      description: 'Drag to block incoming lasers',
      mode: GameMode.deflect,
      duration: const Duration(seconds: 75),
      goal: 30,
      speed: 1.8,
      theme: NeonThemes.iceBlue,
      difficulty: DifficultyLevel.hard,
      xpReward: 320,
      tokenReward: 65,
      instructions: [
        'Drag shield to block lasers',
        'Deflect 30 laser beams',
        'Don\'t let any through!'
      ],
      modeSpecificConfig: {
        'laserSpeed': 200.0,
        'shieldSize': 80.0,
        'laserSpawnRate': 1.5,
      },
    ),

    // Level 10: Survive
    LevelConfig(
      levelNumber: 10,
      title: 'Neon Chaos',
      description: 'Survive 60 seconds of pure chaos',
      mode: GameMode.survive,
      duration: const Duration(seconds: 60),
      goal: 60,
      speed: 2.0,
      theme: NeonThemes.rainbow,
      difficulty: DifficultyLevel.expert,
      xpReward: 500,
      tokenReward: 100,
      instructions: [
        'Survive the neon chaos',
        'Avoid everything for 60 seconds',
        'Use all your skills!'
      ],
      modeSpecificConfig: {
        'chaosIntensity': 1.0,
        'effectCount': 5,
        'survivalBonus': 10,
      },
    ),
  ];

  /// Get level config by number
  static LevelConfig? getLevelConfig(int levelNumber) {
    try {
      return allLevels.firstWhere((level) => level.levelNumber == levelNumber);
    } catch (e) {
      return null;
    }
  }

  /// Get all unlocked levels for a player level
  static List<LevelConfig> getUnlockedLevels(int playerLevel) {
    return allLevels.where((level) => level.levelNumber <= playerLevel).toList();
  }
}
