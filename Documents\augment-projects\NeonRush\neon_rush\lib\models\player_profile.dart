import '../core/constants.dart';
import 'power_up.dart';

/// Represents the player's profile and progress
class PlayerProfile {
  final String playerId;
  final String playerName;
  final int level;
  final int xp;
  final int tokens;
  final int highestLevelUnlocked;
  final List<OwnedPowerUp> ownedPowerUps;
  final List<String> unlockedThemes;
  final String currentThemeId;
  final Map<int, LevelStats> levelStats;
  final DateTime createdAt;
  final DateTime lastPlayedAt;
  final PlayerSettings settings;

  const PlayerProfile({
    required this.playerId,
    required this.playerName,
    required this.level,
    required this.xp,
    required this.tokens,
    required this.highestLevelUnlocked,
    required this.ownedPowerUps,
    required this.unlockedThemes,
    required this.currentThemeId,
    required this.levelStats,
    required this.createdAt,
    required this.lastPlayedAt,
    required this.settings,
  });

  /// Creates a new player profile with default values
  factory PlayerProfile.newPlayer({
    required String playerId,
    required String playerName,
  }) {
    final now = DateTime.now();
    return PlayerProfile(
      playerId: playerId,
      playerName: playerName,
      level: 1,
      xp: 0,
      tokens: GameConstants.startingTokens,
      highestLevelUnlocked: 1,
      ownedPowerUps: [],
      unlockedThemes: ['cyber_blue'], // Start with first theme
      currentThemeId: 'cyber_blue',
      levelStats: {},
      createdAt: now,
      lastPlayedAt: now,
      settings: PlayerSettings.defaultSettings(),
    );
  }

  /// Creates a copy of this profile with modified properties
  PlayerProfile copyWith({
    String? playerId,
    String? playerName,
    int? level,
    int? xp,
    int? tokens,
    int? highestLevelUnlocked,
    List<OwnedPowerUp>? ownedPowerUps,
    List<String>? unlockedThemes,
    String? currentThemeId,
    Map<int, LevelStats>? levelStats,
    DateTime? createdAt,
    DateTime? lastPlayedAt,
    PlayerSettings? settings,
  }) {
    return PlayerProfile(
      playerId: playerId ?? this.playerId,
      playerName: playerName ?? this.playerName,
      level: level ?? this.level,
      xp: xp ?? this.xp,
      tokens: tokens ?? this.tokens,
      highestLevelUnlocked: highestLevelUnlocked ?? this.highestLevelUnlocked,
      ownedPowerUps: ownedPowerUps ?? this.ownedPowerUps,
      unlockedThemes: unlockedThemes ?? this.unlockedThemes,
      currentThemeId: currentThemeId ?? this.currentThemeId,
      levelStats: levelStats ?? this.levelStats,
      createdAt: createdAt ?? this.createdAt,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      settings: settings ?? this.settings,
    );
  }

  /// Calculate XP needed for next level
  int get xpNeededForNextLevel {
    final nextLevelXp = level * GameConstants.xpPerLevel;
    return nextLevelXp - xp;
  }

  /// Calculate progress to next level (0.0 to 1.0)
  double get levelProgress {
    final currentLevelXp = (level - 1) * GameConstants.xpPerLevel;
    final nextLevelXp = level * GameConstants.xpPerLevel;
    final progressXp = xp - currentLevelXp;
    final totalXpNeeded = nextLevelXp - currentLevelXp;
    return (progressXp / totalXpNeeded).clamp(0.0, 1.0);
  }

  /// Add XP and handle level ups
  PlayerProfile addXp(int xpToAdd) {
    final newXp = xp + xpToAdd;
    final newLevel = (newXp / GameConstants.xpPerLevel).floor() + 1;
    
    return copyWith(
      xp: newXp,
      level: newLevel,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Add tokens
  PlayerProfile addTokens(int tokensToAdd) {
    return copyWith(
      tokens: tokens + tokensToAdd,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Spend tokens
  PlayerProfile spendTokens(int tokensToSpend) {
    if (tokens < tokensToSpend) {
      throw Exception('Insufficient tokens');
    }
    return copyWith(
      tokens: tokens - tokensToSpend,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Unlock a new level
  PlayerProfile unlockLevel(int levelNumber) {
    if (levelNumber <= highestLevelUnlocked) {
      return this; // Already unlocked
    }
    return copyWith(
      highestLevelUnlocked: levelNumber,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Add a power-up to inventory
  PlayerProfile addPowerUp(String powerUpId, int quantity) {
    final existingIndex = ownedPowerUps.indexWhere((p) => p.powerUpId == powerUpId);
    final newOwnedPowerUps = List<OwnedPowerUp>.from(ownedPowerUps);
    
    if (existingIndex >= 0) {
      final existing = ownedPowerUps[existingIndex];
      newOwnedPowerUps[existingIndex] = existing.copyWith(
        quantity: existing.quantity + quantity,
      );
    } else {
      newOwnedPowerUps.add(OwnedPowerUp(
        powerUpId: powerUpId,
        quantity: quantity,
        acquiredAt: DateTime.now(),
      ));
    }
    
    return copyWith(
      ownedPowerUps: newOwnedPowerUps,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Use a power-up (decrease quantity)
  PlayerProfile usePowerUp(String powerUpId) {
    final existingIndex = ownedPowerUps.indexWhere((p) => p.powerUpId == powerUpId);
    if (existingIndex < 0) {
      throw Exception('Power-up not found');
    }
    
    final existing = ownedPowerUps[existingIndex];
    if (existing.quantity <= 0) {
      throw Exception('No power-ups remaining');
    }
    
    final newOwnedPowerUps = List<OwnedPowerUp>.from(ownedPowerUps);
    if (existing.quantity == 1) {
      newOwnedPowerUps.removeAt(existingIndex);
    } else {
      newOwnedPowerUps[existingIndex] = existing.copyWith(
        quantity: existing.quantity - 1,
      );
    }
    
    return copyWith(
      ownedPowerUps: newOwnedPowerUps,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Update level statistics
  PlayerProfile updateLevelStats(int levelNumber, LevelStats stats) {
    final newLevelStats = Map<int, LevelStats>.from(levelStats);
    newLevelStats[levelNumber] = stats;
    
    return copyWith(
      levelStats: newLevelStats,
      lastPlayedAt: DateTime.now(),
    );
  }

  /// Converts profile to JSON
  Map<String, dynamic> toJson() {
    return {
      'playerId': playerId,
      'playerName': playerName,
      'level': level,
      'xp': xp,
      'tokens': tokens,
      'highestLevelUnlocked': highestLevelUnlocked,
      'ownedPowerUps': ownedPowerUps.map((p) => p.toJson()).toList(),
      'unlockedThemes': unlockedThemes,
      'currentThemeId': currentThemeId,
      'levelStats': levelStats.map((k, v) => MapEntry(k.toString(), v.toJson())),
      'createdAt': createdAt.toIso8601String(),
      'lastPlayedAt': lastPlayedAt.toIso8601String(),
      'settings': settings.toJson(),
    };
  }

  /// Creates profile from JSON
  factory PlayerProfile.fromJson(Map<String, dynamic> json) {
    return PlayerProfile(
      playerId: json['playerId'],
      playerName: json['playerName'],
      level: json['level'],
      xp: json['xp'],
      tokens: json['tokens'],
      highestLevelUnlocked: json['highestLevelUnlocked'],
      ownedPowerUps: (json['ownedPowerUps'] as List)
          .map((p) => OwnedPowerUp.fromJson(p))
          .toList(),
      unlockedThemes: List<String>.from(json['unlockedThemes']),
      currentThemeId: json['currentThemeId'],
      levelStats: (json['levelStats'] as Map<String, dynamic>).map(
        (k, v) => MapEntry(int.parse(k), LevelStats.fromJson(v)),
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastPlayedAt: DateTime.parse(json['lastPlayedAt']),
      settings: PlayerSettings.fromJson(json['settings']),
    );
  }
}

/// Statistics for a specific level
class LevelStats {
  final int timesPlayed;
  final int timesCompleted;
  final int bestScore;
  final Duration bestTime;
  final DateTime lastPlayed;

  const LevelStats({
    required this.timesPlayed,
    required this.timesCompleted,
    required this.bestScore,
    required this.bestTime,
    required this.lastPlayed,
  });

  /// Creates default stats for a new level
  factory LevelStats.initial() {
    return LevelStats(
      timesPlayed: 0,
      timesCompleted: 0,
      bestScore: 0,
      bestTime: Duration.zero,
      lastPlayed: DateTime.now(),
    );
  }

  /// Creates a copy with modified properties
  LevelStats copyWith({
    int? timesPlayed,
    int? timesCompleted,
    int? bestScore,
    Duration? bestTime,
    DateTime? lastPlayed,
  }) {
    return LevelStats(
      timesPlayed: timesPlayed ?? this.timesPlayed,
      timesCompleted: timesCompleted ?? this.timesCompleted,
      bestScore: bestScore ?? this.bestScore,
      bestTime: bestTime ?? this.bestTime,
      lastPlayed: lastPlayed ?? this.lastPlayed,
    );
  }

  /// Calculate completion rate
  double get completionRate {
    if (timesPlayed == 0) return 0.0;
    return timesCompleted / timesPlayed;
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'timesPlayed': timesPlayed,
      'timesCompleted': timesCompleted,
      'bestScore': bestScore,
      'bestTime': bestTime.inMilliseconds,
      'lastPlayed': lastPlayed.toIso8601String(),
    };
  }

  /// Creates from JSON
  factory LevelStats.fromJson(Map<String, dynamic> json) {
    return LevelStats(
      timesPlayed: json['timesPlayed'],
      timesCompleted: json['timesCompleted'],
      bestScore: json['bestScore'],
      bestTime: Duration(milliseconds: json['bestTime']),
      lastPlayed: DateTime.parse(json['lastPlayed']),
    );
  }
}

/// Player settings and preferences
class PlayerSettings {
  final double soundVolume;
  final double musicVolume;
  final bool vibrationEnabled;
  final bool notificationsEnabled;
  final String preferredLanguage;

  const PlayerSettings({
    required this.soundVolume,
    required this.musicVolume,
    required this.vibrationEnabled,
    required this.notificationsEnabled,
    required this.preferredLanguage,
  });

  /// Creates default settings
  factory PlayerSettings.defaultSettings() {
    return const PlayerSettings(
      soundVolume: 0.8,
      musicVolume: 0.6,
      vibrationEnabled: true,
      notificationsEnabled: true,
      preferredLanguage: 'en',
    );
  }

  /// Creates a copy with modified properties
  PlayerSettings copyWith({
    double? soundVolume,
    double? musicVolume,
    bool? vibrationEnabled,
    bool? notificationsEnabled,
    String? preferredLanguage,
  }) {
    return PlayerSettings(
      soundVolume: soundVolume ?? this.soundVolume,
      musicVolume: musicVolume ?? this.musicVolume,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'soundVolume': soundVolume,
      'musicVolume': musicVolume,
      'vibrationEnabled': vibrationEnabled,
      'notificationsEnabled': notificationsEnabled,
      'preferredLanguage': preferredLanguage,
    };
  }

  /// Creates from JSON
  factory PlayerSettings.fromJson(Map<String, dynamic> json) {
    return PlayerSettings(
      soundVolume: json['soundVolume'] ?? 0.8,
      musicVolume: json['musicVolume'] ?? 0.6,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      preferredLanguage: json['preferredLanguage'] ?? 'en',
    );
  }
}
