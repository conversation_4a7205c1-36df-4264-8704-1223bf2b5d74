import 'package:flutter/material.dart';

/// Game constants and configuration values
class GameConstants {
  // Game settings
  static const String gameTitle = 'NeonRush';
  static const double gameWidth = 400.0;
  static const double gameHeight = 800.0;
  
  // Level progression
  static const int maxLevels = 10;
  static const int baseXpReward = 100;
  static const int baseTokenReward = 25;
  
  // Player progression
  static const int xpPerLevel = 1000;
  static const int startingTokens = 100;
  
  // Power-up costs
  static const int slowMotionCost = 50;
  static const int neonBombCost = 75;
  static const int shieldCost = 30;
  static const int magnetTouchCost = 60;
  static const int timeBoostCost = 40;
  
  // Crate rewards
  static const int basicCrateMinTokens = 10;
  static const int basicCrateMaxTokens = 50;
  static const int advancedCrateMinTokens = 50;
  static const int advancedCrateMaxTokens = 150;
  static const int epicCrateMinTokens = 100;
  static const int epicCrateMaxTokens = 300;
  
  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 600);
  static const Duration longAnimation = Duration(milliseconds: 1000);
  
  // Neon glow settings
  static const double glowRadius = 20.0;
  static const double glowSpread = 5.0;
  static const double pulseScale = 1.2;
}

/// Game mode enumeration
enum GameMode {
  tap,
  swipe,
  hold,
  avoid,
  drag,
  memory,
  spin,
  reaction,
  deflect,
  survive
}

/// Crate type enumeration
enum CrateType {
  basic,
  advanced,
  epic
}

/// Power-up type enumeration
enum PowerUpType {
  slowMotion,
  neonBomb,
  shield,
  magnetTouch,
  timeBoost
}

/// Game state enumeration
enum GameState {
  menu,
  playing,
  paused,
  gameOver,
  levelComplete,
  shop,
  crateOpening
}

/// Difficulty level enumeration
enum DifficultyLevel {
  easy,
  medium,
  hard,
  expert
}
