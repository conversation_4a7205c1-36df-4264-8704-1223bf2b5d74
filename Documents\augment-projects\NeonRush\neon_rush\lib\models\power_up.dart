import 'package:flutter/material.dart';
import '../core/constants.dart';

/// Represents a power-up that can be used in the game
class PowerUp {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final int cost;
  final Duration duration;
  final PowerUpType type;
  final Color color;
  final bool isConsumable;
  final Map<String, dynamic> effects;

  const PowerUp({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.cost,
    required this.duration,
    required this.type,
    required this.color,
    this.isConsumable = true,
    this.effects = const {},
  });

  /// Creates a copy of this power-up with modified properties
  PowerUp copyWith({
    String? id,
    String? name,
    String? description,
    IconData? icon,
    int? cost,
    Duration? duration,
    PowerUpType? type,
    Color? color,
    bool? isConsumable,
    Map<String, dynamic>? effects,
  }) {
    return PowerUp(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      cost: cost ?? this.cost,
      duration: duration ?? this.duration,
      type: type ?? this.type,
      color: color ?? this.color,
      isConsumable: isConsumable ?? this.isConsumable,
      effects: effects ?? this.effects,
    );
  }

  /// Converts power-up to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon.codePoint,
      'cost': cost,
      'duration': duration.inMilliseconds,
      'type': type.name,
      'color': color.value,
      'isConsumable': isConsumable,
      'effects': effects,
    };
  }

  /// Creates power-up from JSON
  factory PowerUp.fromJson(Map<String, dynamic> json) {
    return PowerUp(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: IconData(json['icon'], fontFamily: 'MaterialIcons'),
      cost: json['cost'],
      duration: Duration(milliseconds: json['duration']),
      type: PowerUpType.values.firstWhere((t) => t.name == json['type']),
      color: Color(json['color']),
      isConsumable: json['isConsumable'] ?? true,
      effects: Map<String, dynamic>.from(json['effects'] ?? {}),
    );
  }
}

/// Predefined power-ups
class PowerUps {
  static const slowMotion = PowerUp(
    id: 'slow_motion',
    name: 'Slow Motion',
    description: 'Slows down all targets for 5 seconds',
    icon: Icons.slow_motion_video,
    cost: GameConstants.slowMotionCost,
    duration: Duration(seconds: 5),
    type: PowerUpType.slowMotion,
    color: Color(0xFF00FFFF),
    effects: {
      'slowFactor': 0.5,
      'affectsAll': true,
    },
  );

  static const neonBomb = PowerUp(
    id: 'neon_bomb',
    name: 'Neon Bomb',
    description: 'Clears all targets currently on screen',
    icon: Icons.flash_on,
    cost: GameConstants.neonBombCost,
    duration: Duration(milliseconds: 500),
    type: PowerUpType.neonBomb,
    color: Color(0xFFFF00FF),
    effects: {
      'clearAll': true,
      'explosionRadius': 200.0,
    },
  );

  static const shield = PowerUp(
    id: 'shield',
    name: 'Shield',
    description: 'Prevents one failure or hit',
    icon: Icons.shield,
    cost: GameConstants.shieldCost,
    duration: Duration(minutes: 1),
    type: PowerUpType.shield,
    color: Color(0xFF00FF00),
    effects: {
      'blockCount': 1,
      'glowEffect': true,
    },
  );

  static const magnetTouch = PowerUp(
    id: 'magnet_touch',
    name: 'Magnet Touch',
    description: 'Increases tap hitbox for 10 seconds',
    icon: Icons.touch_app,
    cost: GameConstants.magnetTouchCost,
    duration: Duration(seconds: 10),
    type: PowerUpType.magnetTouch,
    color: Color(0xFFFFD700),
    effects: {
      'hitboxMultiplier': 2.0,
      'magnetRange': 50.0,
    },
  );

  static const timeBoost = PowerUp(
    id: 'time_boost',
    name: 'Time Boost',
    description: 'Adds 5 seconds to the level timer',
    icon: Icons.access_time,
    cost: GameConstants.timeBoostCost,
    duration: Duration(milliseconds: 100),
    type: PowerUpType.timeBoost,
    color: Color(0xFFFF8000),
    effects: {
      'timeAdd': 5.0,
      'instantEffect': true,
    },
  );

  /// Get all available power-ups
  static List<PowerUp> get allPowerUps => [
        slowMotion,
        neonBomb,
        shield,
        magnetTouch,
        timeBoost,
      ];

  /// Get power-up by ID
  static PowerUp? getPowerUpById(String id) {
    try {
      return allPowerUps.firstWhere((powerUp) => powerUp.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get power-ups by type
  static List<PowerUp> getPowerUpsByType(PowerUpType type) {
    return allPowerUps.where((powerUp) => powerUp.type == type).toList();
  }
}

/// Represents an owned power-up instance
class OwnedPowerUp {
  final String powerUpId;
  final int quantity;
  final DateTime acquiredAt;
  final bool isEquipped;

  const OwnedPowerUp({
    required this.powerUpId,
    required this.quantity,
    required this.acquiredAt,
    this.isEquipped = false,
  });

  /// Creates a copy of this owned power-up with modified properties
  OwnedPowerUp copyWith({
    String? powerUpId,
    int? quantity,
    DateTime? acquiredAt,
    bool? isEquipped,
  }) {
    return OwnedPowerUp(
      powerUpId: powerUpId ?? this.powerUpId,
      quantity: quantity ?? this.quantity,
      acquiredAt: acquiredAt ?? this.acquiredAt,
      isEquipped: isEquipped ?? this.isEquipped,
    );
  }

  /// Converts owned power-up to JSON
  Map<String, dynamic> toJson() {
    return {
      'powerUpId': powerUpId,
      'quantity': quantity,
      'acquiredAt': acquiredAt.toIso8601String(),
      'isEquipped': isEquipped,
    };
  }

  /// Creates owned power-up from JSON
  factory OwnedPowerUp.fromJson(Map<String, dynamic> json) {
    return OwnedPowerUp(
      powerUpId: json['powerUpId'],
      quantity: json['quantity'],
      acquiredAt: DateTime.parse(json['acquiredAt']),
      isEquipped: json['isEquipped'] ?? false,
    );
  }

  /// Get the actual power-up data
  PowerUp? get powerUp => PowerUps.getPowerUpById(powerUpId);
}
