import 'package:flutter/material.dart';

/// Represents a neon theme with colors and visual settings
class NeonTheme {
  final String id;
  final String name;
  final Color primary;
  final Color secondary;
  final Color accent;
  final Color background;
  final String? backgroundImage;
  final String soundSet;
  final List<Color> gradientColors;
  final double glowIntensity;

  const NeonTheme({
    required this.id,
    required this.name,
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.background,
    this.backgroundImage,
    required this.soundSet,
    required this.gradientColors,
    this.glowIntensity = 1.0,
  });

  /// Creates a copy of this theme with modified properties
  NeonTheme copyWith({
    String? id,
    String? name,
    Color? primary,
    Color? secondary,
    Color? accent,
    Color? background,
    String? backgroundImage,
    String? soundSet,
    List<Color>? gradientColors,
    double? glowIntensity,
  }) {
    return NeonTheme(
      id: id ?? this.id,
      name: name ?? this.name,
      primary: primary ?? this.primary,
      secondary: secondary ?? this.secondary,
      accent: accent ?? this.accent,
      background: background ?? this.background,
      backgroundImage: backgroundImage ?? this.backgroundImage,
      soundSet: soundSet ?? this.soundSet,
      gradientColors: gradientColors ?? this.gradientColors,
      glowIntensity: glowIntensity ?? this.glowIntensity,
    );
  }

  /// Converts theme to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'primary': primary.value,
      'secondary': secondary.value,
      'accent': accent.value,
      'background': background.value,
      'backgroundImage': backgroundImage,
      'soundSet': soundSet,
      'gradientColors': gradientColors.map((c) => c.value).toList(),
      'glowIntensity': glowIntensity,
    };
  }

  /// Creates theme from JSON
  factory NeonTheme.fromJson(Map<String, dynamic> json) {
    return NeonTheme(
      id: json['id'],
      name: json['name'],
      primary: Color(json['primary']),
      secondary: Color(json['secondary']),
      accent: Color(json['accent']),
      background: Color(json['background']),
      backgroundImage: json['backgroundImage'],
      soundSet: json['soundSet'],
      gradientColors: (json['gradientColors'] as List)
          .map((c) => Color(c))
          .toList(),
      glowIntensity: json['glowIntensity'] ?? 1.0,
    );
  }
}

/// Predefined neon themes for different levels
class NeonThemes {
  static const cyberBlue = NeonTheme(
    id: 'cyber_blue',
    name: 'Cyber Blue',
    primary: Color(0xFF00FFFF),
    secondary: Color(0xFF0080FF),
    accent: Color(0xFF80FFFF),
    background: Color(0xFF001122),
    soundSet: 'cyber',
    gradientColors: [Color(0xFF00FFFF), Color(0xFF0080FF)],
  );

  static const neonPink = NeonTheme(
    id: 'neon_pink',
    name: 'Neon Pink',
    primary: Color(0xFFFF00FF),
    secondary: Color(0xFF8000FF),
    accent: Color(0xFFFF80FF),
    background: Color(0xFF220022),
    soundSet: 'synth',
    gradientColors: [Color(0xFFFF00FF), Color(0xFF8000FF)],
  );

  static const electricGreen = NeonTheme(
    id: 'electric_green',
    name: 'Electric Green',
    primary: Color(0xFF00FF00),
    secondary: Color(0xFF80FF00),
    accent: Color(0xFF40FF40),
    background: Color(0xFF002200),
    soundSet: 'electric',
    gradientColors: [Color(0xFF00FF00), Color(0xFF80FF00)],
  );

  static const fireOrange = NeonTheme(
    id: 'fire_orange',
    name: 'Fire Orange',
    primary: Color(0xFFFF4000),
    secondary: Color(0xFFFF8000),
    accent: Color(0xFFFFFF00),
    background: Color(0xFF220000),
    soundSet: 'fire',
    gradientColors: [Color(0xFFFF4000), Color(0xFFFF8000)],
  );

  static const purpleHaze = NeonTheme(
    id: 'purple_haze',
    name: 'Purple Haze',
    primary: Color(0xFF8000FF),
    secondary: Color(0xFF4000FF),
    accent: Color(0xFFFF00FF),
    background: Color(0xFF110022),
    soundSet: 'mystic',
    gradientColors: [Color(0xFF8000FF), Color(0xFF4000FF)],
  );

  static const goldRush = NeonTheme(
    id: 'gold_rush',
    name: 'Gold Rush',
    primary: Color(0xFFFFD700),
    secondary: Color(0xFFFF8C00),
    accent: Color(0xFFFFFF80),
    background: Color(0xFF221100),
    soundSet: 'golden',
    gradientColors: [Color(0xFFFFD700), Color(0xFFFF8C00)],
  );

  static const iceBlue = NeonTheme(
    id: 'ice_blue',
    name: 'Ice Blue',
    primary: Color(0xFF80FFFF),
    secondary: Color(0xFF4080FF),
    accent: Color(0xFFFFFFFF),
    background: Color(0xFF001122),
    soundSet: 'ice',
    gradientColors: [Color(0xFF80FFFF), Color(0xFF4080FF)],
  );

  static const crimsonRed = NeonTheme(
    id: 'crimson_red',
    name: 'Crimson Red',
    primary: Color(0xFFFF0040),
    secondary: Color(0xFFFF4080),
    accent: Color(0xFFFF8080),
    background: Color(0xFF220011),
    soundSet: 'crimson',
    gradientColors: [Color(0xFFFF0040), Color(0xFFFF4080)],
  );

  static const silverStorm = NeonTheme(
    id: 'silver_storm',
    name: 'Silver Storm',
    primary: Color(0xFFC0C0C0),
    secondary: Color(0xFF808080),
    accent: Color(0xFFFFFFFF),
    background: Color(0xFF111111),
    soundSet: 'storm',
    gradientColors: [Color(0xFFC0C0C0), Color(0xFF808080)],
  );

  static const rainbow = NeonTheme(
    id: 'rainbow',
    name: 'Rainbow Chaos',
    primary: Color(0xFFFF0080),
    secondary: Color(0xFF80FF00),
    accent: Color(0xFF0080FF),
    background: Color(0xFF000000),
    soundSet: 'chaos',
    gradientColors: [
      Color(0xFFFF0000),
      Color(0xFFFF8000),
      Color(0xFFFFFF00),
      Color(0xFF00FF00),
      Color(0xFF0080FF),
      Color(0xFF8000FF),
      Color(0xFFFF00FF),
    ],
  );

  /// Get all available themes
  static List<NeonTheme> get allThemes => [
        cyberBlue,
        neonPink,
        electricGreen,
        fireOrange,
        purpleHaze,
        goldRush,
        iceBlue,
        crimsonRed,
        silverStorm,
        rainbow,
      ];

  /// Get theme by ID
  static NeonTheme? getThemeById(String id) {
    try {
      return allThemes.firstWhere((theme) => theme.id == id);
    } catch (e) {
      return null;
    }
  }
}
